//===-- VIROps.h - Dynamic Vector IR Operation Declarations -----*- C++ -*-===//
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//===----------------------------------------------------------------------===//
//
// This file declares the operation set for the Dynamic Vector IR (VIR) dialect,
// including vector length management and dynamic vector operations.
//
//===----------------------------------------------------------------------===//

#ifndef VIR_VIROPS_H
#define VIR_VIROPS_H

#include "mlir/Bytecode/BytecodeOpInterface.h"
#include "mlir/IR/BuiltinTypes.h"
#include "mlir/IR/Dialect.h"
#include "mlir/IR/OpDefinition.h"
#include "mlir/Interfaces/InferTypeOpInterface.h"
#include "mlir/Interfaces/SideEffectInterfaces.h"

#include "VIR/VIRTypes.h"

#define GET_OP_CLASSES
#include "VIR/VIR.h.inc"

#endif // VIR_VIROPS_H
