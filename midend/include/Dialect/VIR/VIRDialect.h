//===-- VIRDialect.h - Dynamic Vector IR Dialect Declaration ----*- C++ -*-===//
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//===----------------------------------------------------------------------===//
//
// This file declares the core dialect structure for the Dynamic Vector IR
// (VIR), including the dialect registration and namespace configuration.
//
//===----------------------------------------------------------------------===//

#ifndef VIR_VIRDIALECT_H
#define VIR_VIRDIALECT_H

#include "mlir/IR/Dialect.h"

#include "VIR/VIRDialect.h.inc"

#endif // VIR_VIRDIALECT_H
